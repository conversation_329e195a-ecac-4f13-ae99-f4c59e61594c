<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AndroidX WebKit 功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .button {
            display: block;
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px;
            margin: 5px 0;
            background-color: #f8f9fa;
            border-radius: 4px;
            border-left: 3px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AndroidX WebKit 功能测试</h1>

        <div class="info">
            <strong>说明：</strong>这个页面用于测试AndroidX WebKit库的各种功能。
            点击下面的按钮来测试不同的功能。
        </div>

        <div id="versionWarning" class="info" style="display: none; background-color: #fff3cd; border-left-color: #ffc107;">
            <strong>⚠️ WebView版本提醒：</strong>
            <div id="versionMessage"></div>
        </div>

        <button class="button" onclick="testWebKitFeatures()">检查AndroidX WebKit功能支持</button>
        <button class="button" onclick="testJavaScriptInterface()">测试JavaScript接口</button>
        <button class="button" onclick="testSunmiDevice()">获取Sunmi设备信息</button>
        <button class="button" onclick="testPrintFunction()">测试打印功能</button>
        <button class="button" onclick="testSoundEffect()">测试支付音效</button>
        <button class="button" onclick="testNetworkConnection()">测试网络连接</button>
        <button class="button" onclick="testWebViewSettings()">查看WebView设置</button>
        <button class="button" onclick="openWebKitTestActivity()">打开WebKit测试Activity</button>

        <div id="result" class="result" style="display: none;">
            <h3>测试结果：</h3>
            <div id="resultContent"></div>
        </div>

        <div class="info">
            <h3>AndroidX WebKit 主要功能：</h3>
            <ul class="feature-list">
                <li>算法暗色模式支持</li>
                <li>安全浏览功能</li>
                <li>多进程模式检测</li>
                <li>离线预渲染</li>
                <li>WebMessage监听器</li>
                <li>文档开始JavaScript注入</li>
                <li>代理覆盖功能</li>
                <li>强制暗色模式</li>
            </ul>
        </div>
    </div>

    <script>
        function showResult(content) {
            const resultDiv = document.getElementById('result');
            const resultContent = document.getElementById('resultContent');
            resultContent.innerHTML = content;
            resultDiv.style.display = 'block';
            resultDiv.scrollIntoView({ behavior: 'smooth' });
        }

        function testWebKitFeatures() {
            try {
                if (typeof Android !== 'undefined' && Android.showWebKitFeatures) {
                    Android.showWebKitFeatures();
                    showResult('AndroidX WebKit功能检查已启动，请查看弹出对话框。');
                } else {
                    showResult('Android接口不可用，请确保在Android WebView中运行。');
                }
            } catch (e) {
                showResult('错误：' + e.message);
            }
        }

        function testJavaScriptInterface() {
            try {
                if (typeof Android !== 'undefined') {
                    const features = [];

                    // 检查可用的方法
                    const methods = ['showWebKitFeatures', 'getSN', 'print', 'playPaySound',
                                   'testNetworkConnection', 'runDebugTools', 'loadUrl'];

                    methods.forEach(method => {
                        if (Android[method]) {
                            features.push(`✓ ${method} - 可用`);
                        } else {
                            features.push(`✗ ${method} - 不可用`);
                        }
                    });

                    showResult('<h4>JavaScript接口检查结果：</h4>' + features.join('<br>'));
                } else {
                    showResult('Android JavaScript接口不可用。');
                }
            } catch (e) {
                showResult('错误：' + e.message);
            }
        }

        function testSunmiDevice() {
            try {
                if (typeof Android !== 'undefined' && Android.getSN) {
                    const sn = Android.getSN();
                    showResult(`<h4>Sunmi设备信息：</h4>设备序列号：${sn}`);
                } else {
                    showResult('getSN方法不可用。');
                }
            } catch (e) {
                showResult('错误：' + e.message);
            }
        }

        function testPrintFunction() {
            try {
                if (typeof Android !== 'undefined' && Android.print) {
                    Android.print();
                    showResult('打印功能已调用，请检查打印机。');
                } else {
                    showResult('print方法不可用。');
                }
            } catch (e) {
                showResult('错误：' + e.message);
            }
        }

        function testSoundEffect() {
            try {
                if (typeof Android !== 'undefined' && Android.playPaySound) {
                    Android.playPaySound();
                    showResult('支付音效已播放。');
                } else {
                    showResult('playPaySound方法不可用。');
                }
            } catch (e) {
                showResult('错误：' + e.message);
            }
        }

        function testNetworkConnection() {
            try {
                if (typeof Android !== 'undefined' && Android.testNetworkConnection) {
                    Android.testNetworkConnection();
                    showResult('网络连接测试已启动，请查看弹出对话框。');
                } else {
                    showResult('testNetworkConnection方法不可用。');
                }
            } catch (e) {
                showResult('错误：' + e.message);
            }
        }

        function testWebViewSettings() {
            try {
                if (typeof Android !== 'undefined' && Android.showWebViewSettings) {
                    Android.showWebViewSettings();
                    showResult('WebView设置查看已启动，请查看弹出对话框。');
                } else {
                    showResult('showWebViewSettings方法不可用。');
                }
            } catch (e) {
                showResult('错误：' + e.message);
            }
        }

        function openWebKitTestActivity() {
            try {
                // 通过loadUrl方法启动测试Activity
                if (typeof Android !== 'undefined' && Android.loadUrl) {
                    // 这里我们使用一个特殊的URL来触发测试Activity
                    Android.loadUrl('webkit://test');
                    showResult('尝试打开WebKit测试Activity。');
                } else {
                    showResult('无法启动WebKit测试Activity。');
                }
            } catch (e) {
                showResult('错误：' + e.message);
            }
        }

        // 检测Chrome版本
        function detectChromeVersion() {
            const userAgent = navigator.userAgent;
            const chromeMatch = userAgent.match(/Chrome\/(\d+)/);
            return chromeMatch ? parseInt(chromeMatch[1]) : 0;
        }

        // 显示版本警告
        function showVersionWarning() {
            const chromeVersion = detectChromeVersion();
            const warningDiv = document.getElementById('versionWarning');
            const messageDiv = document.getElementById('versionMessage');

            if (chromeVersion > 0 && chromeVersion < 70) {
                let message = `检测到Chrome ${chromeVersion}版本 (2017年)，建议升级到70+版本以获得更好体验。<br><br>`;
                message += `<button onclick="showUpgradeSuggestions()" style="background: #ffc107; color: #000; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">查看升级建议</button> `;
                message += `<button onclick="enableCompatibilityMode()" style="background: #6c757d; color: #fff; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">启用兼容模式</button>`;

                messageDiv.innerHTML = message;
                warningDiv.style.display = 'block';
            } else if (chromeVersion >= 70) {
                messageDiv.innerHTML = `✅ Chrome ${chromeVersion}版本，支持良好！`;
                warningDiv.style.backgroundColor = '#d4edda';
                warningDiv.style.borderLeftColor = '#28a745';
                warningDiv.style.display = 'block';
            }
        }

        // 显示升级建议
        function showUpgradeSuggestions() {
            if (typeof Android !== 'undefined' && Android.showWebViewUpgradeSuggestions) {
                Android.showWebViewUpgradeSuggestions();
            } else {
                alert('升级建议：\n\n1. 打开Google Play商店\n2. 搜索"Android System WebView"\n3. 点击更新\n\n或者升级Android系统版本');
            }
        }

        // 启用兼容模式
        function enableCompatibilityMode() {
            if (typeof Android !== 'undefined' && Android.enableCompatibilityMode) {
                Android.enableCompatibilityMode();

                // 更新页面显示
                const messageDiv = document.getElementById('versionMessage');
                messageDiv.innerHTML = '✅ 兼容模式已启用，应用将自动适配当前WebView版本。';
                document.getElementById('versionWarning').style.backgroundColor = '#d4edda';
                document.getElementById('versionWarning').style.borderLeftColor = '#28a745';
            } else {
                alert('兼容模式启用失败，请确保在Android WebView中运行。');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AndroidX WebKit测试页面已加载');

            // 检查Android接口是否可用
            if (typeof Android !== 'undefined') {
                console.log('Android JavaScript接口可用');
            } else {
                console.log('Android JavaScript接口不可用');
            }

            // 显示版本信息
            showVersionWarning();

            // 记录用户代理
            console.log('User Agent:', navigator.userAgent);
            console.log('Chrome版本:', detectChromeVersion());
        });
    </script>
</body>
</html>
