<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- WebView 容器 -->
    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- 相机预览容器 -->
    <FrameLayout
        android:id="@+id/camera_preview_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />
        
    <!-- 调试工具面板 -->
    <LinearLayout
        android:id="@+id/debug_panel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_alignParentEnd="true"
        android:orientation="vertical"
        android:background="#80000000"
        android:padding="8dp">
        
        <Button
            android:id="@+id/btn_debug"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="调试工具"
            android:textSize="12sp"
            android:padding="8dp"
            android:background="#F44336"
            android:textColor="#FFFFFF" />
            
        <Button
            android:id="@+id/btn_switch_url"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="切换网址"
            android:textSize="12sp"
            android:layout_marginTop="4dp"
            android:padding="8dp"
            android:background="#2196F3"
            android:textColor="#FFFFFF" />
    </LinearLayout>

</RelativeLayout>