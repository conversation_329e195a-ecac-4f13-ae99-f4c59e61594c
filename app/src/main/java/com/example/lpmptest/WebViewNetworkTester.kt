package com.example.lpmptest

import android.util.Log
import android.webkit.URLUtil
import android.webkit.WebResourceResponse
import android.webkit.WebView
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.HttpURLConnection
import java.net.URL
import java.security.cert.X509Certificate
import javax.net.ssl.HttpsURLConnection
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager
import kotlin.concurrent.thread

/**
 * WebView网络诊断工具
 */
object WebViewNetworkTester {
    
    private const val TAG = "WebViewNetworkTester"
    
    /**
     * 测试URL连接
     * @param urlString 要测试的URL
     * @param callback 测试结果回调
     */
    fun testUrlConnection(urlString: String, callback: (TestResult) -> Unit) {
        thread {
            try {
                val result = TestResult(urlString)
                
                // 检查URL格式
                if (!URLUtil.isValidUrl(urlString)) {
                    result.isSuccess = false
                    result.errorMessage = "无效的URL格式"
                    callback(result)
                    return@thread
                }
                
                // 创建URL连接
                val url = URL(urlString)
                val connection = if (url.protocol == "https") {
                    setupTrustAllCerts()
                    url.openConnection() as HttpsURLConnection
                } else {
                    url.openConnection() as HttpURLConnection
                }
                
                try {
                    // 设置连接参数
                    connection.connectTimeout = 10000
                    connection.readTimeout = 10000
                    connection.instanceFollowRedirects = true
                    connection.requestMethod = "GET"
                    connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Linux; Android 10; Mobile)")
                    
                    // 记录开始时间
                    val startTime = System.currentTimeMillis()
                    
                    // 建立连接
                    connection.connect()
                    
                    // 获取响应
                    val responseCode = connection.responseCode
                    result.responseCode = responseCode
                    result.responseTime = System.currentTimeMillis() - startTime
                    
                    // 获取响应头
                    connection.headerFields.forEach { (key, value) ->
                        if (key != null) {
                            result.headers[key] = value.joinToString("; ")
                        }
                    }
                    
                    // 读取响应内容
                    if (responseCode in 200..299) {
                        val reader = BufferedReader(InputStreamReader(connection.inputStream))
                        val response = StringBuilder()
                        var line: String?
                        var lineCount = 0
                        while (reader.readLine().also { line = it } != null && lineCount < 20) {
                            response.append(line).append("\n")
                            lineCount++
                        }
                        reader.close()
                        
                        result.contentSnippet = response.toString()
                        result.contentType = connection.contentType
                        result.isSuccess = true
                    } else {
                        // 错误响应
                        result.isSuccess = false
                        result.errorMessage = "HTTP错误: $responseCode"
                        try {
                            val reader = BufferedReader(InputStreamReader(connection.errorStream))
                            val response = StringBuilder()
                            var line: String?
                            var lineCount = 0
                            while (reader.readLine().also { line = it } != null && lineCount < 10) {
                                response.append(line).append("\n")
                                lineCount++
                            }
                            reader.close()
                            result.contentSnippet = response.toString()
                        } catch (e: Exception) {
                            // 忽略读取错误流的异常
                        }
                    }
                } finally {
                    connection.disconnect()
                }
                
                callback(result)
            } catch (e: Exception) {
                Log.e(TAG, "URL连接测试失败: ${e.message}", e)
                val result = TestResult(urlString)
                result.isSuccess = false
                result.errorMessage = "连接异常: ${e.javaClass.simpleName} - ${e.message}"
                callback(result)
            }
        }
    }
    
    /**
     * 测试WebView的URL加载能力
     */
    fun testWebViewLoading(webView: WebView, urlString: String, callback: (TestResult) -> Unit) {
        // 首先测试URL连接
        testUrlConnection(urlString) { connectionResult ->
            // 如果连接测试成功，尝试在WebView中加载
            if (connectionResult.isSuccess) {
                webView.post {
                    try {
                        val startTime = System.currentTimeMillis()
                        
                        // 监听页面加载完成
                        webView.webViewClient = object : android.webkit.WebViewClient() {
                            override fun onPageFinished(view: WebView?, url: String?) {
                                super.onPageFinished(view, url)
                                val loadTime = System.currentTimeMillis() - startTime
                                connectionResult.webViewLoadTime = loadTime
                                callback(connectionResult)
                            }
                            
                            override fun onReceivedError(
                                view: WebView?,
                                errorCode: Int,
                                description: String?,
                                failingUrl: String?
                            ) {
                                super.onReceivedError(view, errorCode, description, failingUrl)
                                connectionResult.isSuccess = false
                                connectionResult.errorMessage = "WebView加载错误: $errorCode - $description"
                                callback(connectionResult)
                            }
                            
                            override fun shouldInterceptRequest(
                                view: WebView?,
                                url: String?
                            ): WebResourceResponse? {
                                if (url != null) {
                                    Log.d(TAG, "WebView加载资源: $url")
                                }
                                return super.shouldInterceptRequest(view, url)
                            }
                        }
                        
                        // 加载URL
                        webView.loadUrl(urlString)
                    } catch (e: Exception) {
                        connectionResult.isSuccess = false
                        connectionResult.errorMessage = "WebView加载异常: ${e.message}"
                        callback(connectionResult)
                    }
                }
            } else {
                // 连接测试失败，直接返回结果
                callback(connectionResult)
            }
        }
    }
    
    /**
     * 配置信任所有SSL证书（仅用于测试）
     */
    private fun setupTrustAllCerts() {
        try {
            val trustAllCerts = arrayOf<TrustManager>(object : X509TrustManager {
                override fun getAcceptedIssuers(): Array<X509Certificate> = arrayOf()
                override fun checkClientTrusted(certs: Array<X509Certificate>, authType: String) {}
                override fun checkServerTrusted(certs: Array<X509Certificate>, authType: String) {}
            })

            val sc = SSLContext.getInstance("TLS")
            sc.init(null, trustAllCerts, java.security.SecureRandom())
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.socketFactory)
            
            // 允许所有主机名
            HttpsURLConnection.setDefaultHostnameVerifier { _, _ -> true }
        } catch (e: Exception) {
            Log.e(TAG, "配置信任所有证书失败", e)
        }
    }
    
    /**
     * URL测试结果
     */
    data class TestResult(
        val url: String,
        var isSuccess: Boolean = false,
        var responseCode: Int = 0,
        var responseTime: Long = 0,
        var contentType: String? = null,
        var contentSnippet: String? = null,
        var errorMessage: String? = null,
        var webViewLoadTime: Long = 0,
        val headers: MutableMap<String, String> = mutableMapOf()
    ) {
        override fun toString(): String {
            val sb = StringBuilder()
            sb.append("URL测试结果: $url\n")
            sb.append("成功: $isSuccess\n")
            
            if (isSuccess) {
                sb.append("响应代码: $responseCode\n")
                sb.append("响应时间: ${responseTime}ms\n")
                if (webViewLoadTime > 0) {
                    sb.append("WebView加载时间: ${webViewLoadTime}ms\n")
                }
                sb.append("内容类型: $contentType\n")
                sb.append("\n关键响应头:\n")
                
                // 显示重要的头信息
                listOf("Content-Type", "Server", "X-Frame-Options", "Content-Security-Policy", 
                      "Access-Control-Allow-Origin", "Location").forEach { key ->
                    if (headers.containsKey(key)) {
                        sb.append("$key: ${headers[key]}\n")
                    }
                }
                
                sb.append("\n内容片段:\n")
                sb.append(contentSnippet)
            } else {
                sb.append("错误: $errorMessage\n")
            }
            
            return sb.toString()
        }
    }
}
