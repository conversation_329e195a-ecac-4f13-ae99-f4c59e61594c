package com.example.lpmptest

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import android.webkit.JavascriptInterface
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import android.widget.Toast
import com.example.lpmptest.ui.theme.LpmpTestTheme
import com.sunmi.peripheral.printer.InnerPrinterCallback
import com.sunmi.peripheral.printer.InnerPrinterManager
import com.sunmi.peripheral.printer.SunmiPrinterService
import android.media.MediaPlayer
import com.example.lpmptest.R
import android.hardware.Camera
import android.view.View
import android.widget.FrameLayout
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

class MainActivity : ComponentActivity() {

    private lateinit var webView: WebView
    private var sunmiPrinter: SunmiPrinterService? = null
    private var camera: Camera? = null
    private var cameraPreview: CameraPreview? = null
    private var isScanning = false
    private lateinit var webViewDebugger: WebViewDebugger

    companion object {
        private const val CAMERA_PERMISSION_REQUEST_CODE = 100
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // 初始化网络安全配置
        android.webkit.WebView.setWebContentsDebuggingEnabled(true) // 启用Chrome远程调试

        webView = findViewById<WebView>(R.id.webview).apply {
            settings.apply {
                javaScriptEnabled = true
                domStorageEnabled = true  // 启用DOM存储
                allowFileAccess = true    // 允许访问文件
                loadsImagesAutomatically = true  // 自动加载图片
                mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW  // 允许混合内容
                useWideViewPort = true    // 使用宽视图
                loadWithOverviewMode = true  // 加载概览模式
                javaScriptCanOpenWindowsAutomatically = true  // 允许JS打开窗口
            }
            webViewClient = object : WebViewClient() {
                override fun onPageFinished(view: WebView?, url: String?) {
                    super.onPageFinished(view, url)
                    Log.d("WebView", "页面加载完成: $url")
                }
                
                override fun onReceivedError(view: WebView?, errorCode: Int, description: String?, failingUrl: String?) {
                    super.onReceivedError(view, errorCode, description, failingUrl)
                    Log.e("WebView", "页面加载错误 - 错误码: $errorCode, 描述: $description, URL: $failingUrl")
                    Toast.makeText(this@MainActivity, "加载页面失败: $description", Toast.LENGTH_LONG).show()
                }
                
                @android.annotation.TargetApi(android.os.Build.VERSION_CODES.M)
                override fun onReceivedError(view: WebView?, request: android.webkit.WebResourceRequest?, error: android.webkit.WebResourceError?) {
                    error?.let {
                        Log.e("WebView", "页面加载错误 - 错误码: ${it.errorCode}, 描述: ${it.description}, URL: ${request?.url}")
                        if (request?.isForMainFrame == true) {
                            Toast.makeText(this@MainActivity, "加载页面失败: ${it.description}", Toast.LENGTH_LONG).show()
                        }
                    }
                    super.onReceivedError(view, request, error)
                }
                
                override fun onReceivedSslError(view: WebView?, handler: android.webkit.SslErrorHandler?, error: android.net.http.SslError?) {
                    Log.e("WebView", "SSL错误: ${error?.toString()}")
                    // SSL证书问题通常是自签名证书或证书不受信任导致的
                    // 在开发环境中可以选择接受所有证书，但在生产环境中不推荐
                    val message = when (error?.primaryError) {
                        android.net.http.SslError.SSL_UNTRUSTED -> "证书颁发机构不受信任"
                        android.net.http.SslError.SSL_EXPIRED -> "证书已过期"
                        android.net.http.SslError.SSL_IDMISMATCH -> "证书主机名不匹配"
                        android.net.http.SslError.SSL_NOTYETVALID -> "证书尚未生效"
                        android.net.http.SslError.SSL_DATE_INVALID -> "证书日期无效"
                        else -> "未知SSL错误"
                    }
                    
                    // 显示SSL错误对话框，让用户选择是否继续
                    android.app.AlertDialog.Builder(this@MainActivity)
                        .setTitle("SSL证书错误")
                        .setMessage("$message\n\n您想继续访问该网站吗？\n错误详情: ${error?.toString()}")
                        .setPositiveButton("继续") { _, _ -> 
                            handler?.proceed() 
                            Log.d("WebView", "用户选择继续访问网站，忽略SSL错误")
                        }
                        .setNegativeButton("取消") { _, _ -> 
                            handler?.cancel()
                            Log.d("WebView", "用户选择取消访问网站")
                        }
                        .create()
                        .show()
                    
                    // 不调用super以便自己处理错误
                    // super.onReceivedSslError(view, handler, error)
                }
            }
            addJavascriptInterface(WebAppInterface(this@MainActivity), "Android")
            // 加载本地 HTML 文件或者远程网页
            // loadUrl("file:///android_asset/index.html")
            loadUrl("https://baidu.com")
            // loadUrl("https://wms.wx-test.sheincorp.cn/#/auth-renew")
            // loadUrl("https://lpmpm-test01.dotfashion.cn/canteen-device#/canteen-device/login")
        }

        // 初始化WebView调试工具
        webViewDebugger = WebViewDebugger(webView)
        
        // 设置调试按钮的点击事件
        findViewById<android.widget.Button>(R.id.btn_debug).setOnClickListener {
            WebAppInterface(this).runDebugTools()
        }
        
        findViewById<android.widget.Button>(R.id.btn_switch_url).setOnClickListener {
            WebAppInterface(this).debugWebView()
        }

        Toast.makeText(this, "欢迎来到主页面！", Toast.LENGTH_SHORT).show()
        // 初始化打印机
        initPrinter()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 1 && resultCode == Activity.RESULT_OK && data != null) {
            val bundle = data.extras
            val result = bundle?.getSerializable("data") as? ArrayList<HashMap<String, String>>
            result?.forEach {
                val type = it["TYPE"]
                val value = it["VALUE"]
                Log.i("sunmi", "Type: $type, Value: $value")
                webView.evaluateJavascript("javascript:onScanResult('$value')", null)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        releaseCamera()
        InnerPrinterManager.getInstance().unBindService(this, null)
    }

    private fun checkCameraPermission() {
        if (ContextCompat.checkSelfPermission(this, android.Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, arrayOf(android.Manifest.permission.CAMERA), CAMERA_PERMISSION_REQUEST_CODE)
        } else {
            initCamera()
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            CAMERA_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    initCamera()
                } else {
                    Toast.makeText(this, "需要相机权限才能使用扫码功能", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun initCamera() {
        try {
            camera = Camera.open()
            camera?.setDisplayOrientation(90)
            cameraPreview = CameraPreview(this).apply {
                setCamera(camera)
            }
        } catch (e: Exception) {
            Log.e("Camera", "Error initializing camera", e)
            Toast.makeText(this, "无法初始化相机", Toast.LENGTH_SHORT).show()
        }
    }

    private fun releaseCamera() {
        try {
            camera?.stopPreview()
            camera?.release()
            camera = null
            cameraPreview = null
            isScanning = false
        } catch (e: Exception) {
            Log.e("Camera", "Error releasing camera", e)
        }
    }


    private fun hasScanner(ctx: Context): Boolean {
        val info = getPackageInfo(ctx, "com.sunmi.scanner")
        val versionName = info?.versionName ?: return false
        return compareVer(versionName, "4.4.4", true, 3)
    }

    private fun getPackageInfo(context: Context, pkg: String): PackageInfo? {
        return try {
            context.packageManager.getPackageInfo(pkg, 0)
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
            null
        }
    }


    private fun compareVer(nVer: String, oVer: String, isEq: Boolean, bit: Int): Boolean {
        if (nVer.isEmpty() || oVer.isEmpty()) return false
        val nArr = nVer.split(".")
        val oArr = oVer.split(".")
        if (nArr.size < bit || oArr.size < bit) return false
        var vup = false
        for (i in 0 until bit) {
            val n = nArr[i].toInt()
            val o = oArr[i].toInt()
            if (n >= o) {
                if (n > o) {
                    vup = true
                    break
                } else if (isEq && i == (bit - 1)) {
                    vup = true
                    break
                }
            } else {
                break
            }
        }
        return vup
    }

    private fun initPrinter() {
        val printerCallback = object : InnerPrinterCallback() {
            // 当 Sunmi 打印机连接成功时的回调
            override fun onConnected(service: SunmiPrinterService) {
                sunmiPrinter = service
                Log.d("Printer", "Sunmi Printer Connected!")
            }

            // 当 Sunmi 打印机断开连接时的回调（提供默认行为防止崩溃）
            override fun onDisconnected() {
                sunmiPrinter = null
                Log.d("Printer", "Sunmi Printer Disconnected!")
            }
        }

        // 绑定 Sunmi 打印服务
        try {
            InnerPrinterManager.getInstance().bindService(this, printerCallback)
        } catch (e: Exception) {
            e.printStackTrace()
            Log.e("Printer", "Failed to bind Sunmi Printer Service", e)
        }
    }


    private fun getSN(): String {
        var serial: String? = null
        try {
            val c = Class.forName("android.os.SystemProperties")
            val get = c.getMethod("get", String::class.java)
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
                serial = get.invoke(c, "ro.sunmi.serial") as String?
            } else if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                serial = android.os.Build.getSerial()
            } else {
                serial = get.invoke(c, "ro.serialno") as String?
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return serial ?: "未知"
    }

    inner class WebAppInterface(private val activity: Activity) {
        // 使用MediaPlayer播放支付成功音效
        private var mediaPlayer: MediaPlayer? = null

        @JavascriptInterface
        fun playPaySound() {
            activity.runOnUiThread {
                try {
                    // 释放之前的MediaPlayer资源
                    mediaPlayer?.release()

                    // 创建新的MediaPlayer并播放支付成功音效
                    mediaPlayer = MediaPlayer.create(activity, R.raw.payment_success)
                    mediaPlayer?.setOnCompletionListener { mp ->
                        mp.release()
                        mediaPlayer = null
                    }
                    mediaPlayer?.start()

                    Toast.makeText(activity, "播放支付成功音效", Toast.LENGTH_SHORT).show()
                } catch (e: Exception) {
                    e.printStackTrace()
                    Toast.makeText(activity, "播放音效失败", Toast.LENGTH_SHORT).show()
                }
            }
        }

        @JavascriptInterface
        fun previewTone() {
            playPaySound()
        }

        @JavascriptInterface
        fun startScan() {
            activity.runOnUiThread {
                if (!isScanning) {
                    checkCameraPermission()
                    val container = activity.findViewById<FrameLayout>(R.id.camera_preview_container)
                    container.visibility = View.VISIBLE
                    cameraPreview?.let { preview ->
                        container.addView(preview)
                    }
                    isScanning = true
                    Toast.makeText(activity, "相机预览已启动", Toast.LENGTH_SHORT).show()
                } else {
                    releaseCamera()
                    val container = activity.findViewById<FrameLayout>(R.id.camera_preview_container)
                    container.visibility = View.GONE
                    container.removeAllViews()
                    Toast.makeText(activity, "相机预览已关闭", Toast.LENGTH_SHORT).show()
                }
            }
        }
        @JavascriptInterface
        fun getSN(): String {
            return <EMAIL>()
        }

        @JavascriptInterface
        fun print() {
            activity.runOnUiThread {
                Toast.makeText(activity, "正在启动打印功能...", Toast.LENGTH_SHORT).show()
            }
            runOnUiThread {
                try {
                    sunmiPrinter?.apply {
                        setAlignment(1, null) // 居中对齐
                        printText("=== 测试打印 ===\n", null)
                        printText("这是一个测试内容\n", null)
                        printText("-------------\n", null)
                        lineWrap(3, null) // 走纸3行
                    }
                } catch (e: Exception) {
                    Toast.makeText(activity, "打印失败", Toast.LENGTH_SHORT).show()
                    e.printStackTrace()
                }
            }
        }

        @JavascriptInterface
        fun loadUrl(url: String) {
            activity.runOnUiThread {
                try {
                    Toast.makeText(activity, "正在加载: $url", Toast.LENGTH_SHORT).show()
                    webView.loadUrl(url)
                } catch (e: Exception) {
                    Toast.makeText(activity, "加载URL失败: ${e.message}", Toast.LENGTH_LONG).show()
                    Log.e("WebView", "加载URL失败", e)
                }
            }
        }
        
        @JavascriptInterface
        fun debugWebView() {
            activity.runOnUiThread {
                val dialog = android.app.AlertDialog.Builder(activity)
                    .setTitle("选择要加载的URL")
                    .setItems(arrayOf(
                        "百度",
                        "SheinCorp",
                        "DotFashion",
                        "查看WebView设置",
                        "网络连接测试"
                    )) { _, which ->
                        when (which) {
                            0 -> webView.loadUrl("https://baidu.com")
                            1 -> webView.loadUrl("https://wms.wx-test.sheincorp.cn/#/auth-renew")
                            2 -> webView.loadUrl("https://lpmpm-test01.dotfashion.cn/canteen-device#/canteen-device/login")
                            3 -> showWebViewSettings()
                            4 -> testNetworkConnection()
                        }
                    }
                    .setNegativeButton("取消", null)
                    .create()
                dialog.show()
            }
        }
        
        @JavascriptInterface
        fun showWebViewSettings() {
            activity.runOnUiThread {
                val settings = webView.settings
                val message = """
                    WebView设置信息:
                    - JavaScript启用: ${settings.javaScriptEnabled}
                    - DOM存储启用: ${settings.domStorageEnabled}
                    - 文件访问: ${settings.allowFileAccess}
                    - 自动加载图片: ${settings.loadsImagesAutomatically}
                    - 混合内容模式: ${settings.mixedContentMode}
                    - UserAgent: ${settings.userAgentString}
                """.trimIndent()
                
                Toast.makeText(activity, "正在查看WebView设置", Toast.LENGTH_SHORT).show()
                Log.d("WebViewSettings", message)
                
                val dialog = android.app.AlertDialog.Builder(activity)
                    .setTitle("WebView设置")
                    .setMessage(message)
                    .setPositiveButton("确定", null)
                    .create()
                dialog.show()
            }
        }

        @JavascriptInterface
        fun logConsole(level: String, message: String) {
            when (level) {
                "ERROR" -> Log.e("WebConsole", message)
                "WARN" -> Log.w("WebConsole", message)
                "INFO" -> Log.i("WebConsole", message)
                else -> Log.d("WebConsole", message)
            }
        }
        
        @JavascriptInterface
        fun logNetwork(type: String, message: String) {
            Log.d("WebNetwork", "$type: $message")
        }
        
        @JavascriptInterface
        fun logPageEvent(event: String, data: String) {
            Log.d("WebPageEvent", "$event: $data")
        }
        
        @JavascriptInterface
        fun testLoadDotfashion() {
            activity.runOnUiThread {
                try {
                    Toast.makeText(activity, "正在测试加载dotfashion...", Toast.LENGTH_SHORT).show()
                    
                    // 1. 先注入调试脚本
                    webViewDebugger.injectDebugScript()
                    
                    // 2. 加载网页
                    webView.loadUrl("https://lpmpm-test01.dotfashion.cn/canteen-device#/canteen-device/login")
                    
                    // 3. 延迟检查加载状态
                    android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                        webViewDebugger.checkLoadingErrors()
                        webViewDebugger.getDOMStructure()
                        webViewDebugger.checkNetworkStatus()
                    }, 5000)
                } catch (e: Exception) {
                    Toast.makeText(activity, "测试加载失败: ${e.message}", Toast.LENGTH_LONG).show()
                    Log.e("WebView", "测试加载失败", e)
                }
            }
        }
        
        @JavascriptInterface
        fun runDebugTools() {
            activity.runOnUiThread {
                try {
                    val dialog = android.app.AlertDialog.Builder(activity)
                        .setTitle("WebView调试工具")
                        .setItems(arrayOf(
                            "检查页面错误",
                            "查看DOM结构",
                            "查看网络状态",
                            "注入调试脚本",
                            "清除缓存并重新加载",
                            "测试dotfashion加载",
                            "网络连接测试",
                            "WebView设置优化"
                        )) { _, which ->
                            when (which) {
                                0 -> webViewDebugger.checkLoadingErrors()
                                1 -> webViewDebugger.getDOMStructure()
                                2 -> webViewDebugger.checkNetworkStatus()
                                3 -> webViewDebugger.injectDebugScript()
                                4 -> {
                                    webView.clearCache(true)
                                    webView.clearHistory()
                                    webView.reload()
                                    Toast.makeText(activity, "已清除缓存并重新加载", Toast.LENGTH_SHORT).show()
                                }
                                5 -> testLoadDotfashion()
                                6 -> testNetworkConnection()
                                7 -> optimizeWebViewSettings()
                            }
                        }
                        .setNegativeButton("取消", null)
                        .create()
                    dialog.show()
                } catch (e: Exception) {
                    Toast.makeText(activity, "调试工具错误: ${e.message}", Toast.LENGTH_LONG).show()
                    Log.e("WebView", "调试工具错误", e)
                }
            }
        }
        
        @JavascriptInterface
        fun optimizeWebViewSettings() {
            activity.runOnUiThread {
                try {
                    // 优化WebView设置
                    webView.settings.apply {
                        // 启用更多功能
                        javaScriptEnabled = true
                        domStorageEnabled = true
                        databaseEnabled = true
                        setAppCacheEnabled(true)
                        allowFileAccess = true
                        allowContentAccess = true
                        loadsImagesAutomatically = true
                        blockNetworkImage = false
                        mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                        
                        // 设置缓存模式
                        cacheMode = android.webkit.WebSettings.LOAD_DEFAULT
                        
                        // 设置UA
                        val originalUA = userAgentString
                        userAgentString = "$originalUA WebViewDebug"
                    }
                    
                    // 显示设置已优化的提示
                    val message = """
                        WebView设置已优化:
                        - JavaScript: 已启用
                        - DOM存储: 已启用
                        - 数据库: 已启用
                        - 应用缓存: 已启用
                        - 文件访问: 已启用
                        - 内容访问: 已启用
                        - 混合内容: 始终允许
                        - 缓存模式: 默认
                        - UA: 已添加标识
                        
                        请重新加载页面以应用新设置。
                    """.trimIndent()
                    
                    android.app.AlertDialog.Builder(activity)
                        .setTitle("WebView设置已优化")
                        .setMessage(message)
                        .setPositiveButton("重新加载") { _, _ -> 
                            webView.reload() 
                        }
                        .setNegativeButton("确定", null)
                        .create()
                        .show()
                    
                } catch (e: Exception) {
                    Toast.makeText(activity, "优化WebView设置失败: ${e.message}", Toast.LENGTH_LONG).show()
                    Log.e("WebView", "优化WebView设置失败", e)
                }
            }
        }

        @JavascriptInterface
        fun testNetworkConnection() {
            activity.runOnUiThread {
                val dialog = android.app.AlertDialog.Builder(activity)
                    .setTitle("网络连接测试")
                    .setItems(arrayOf(
                        "测试 baidu.com",
                        "测试 sheincorp.cn",
                        "测试 dotfashion.cn",
                        "自定义URL测试"
                    )) { _, which ->
                        when (which) {
                            0 -> testUrl("https://baidu.com")
                            1 -> testUrl("https://wms.wx-test.sheincorp.cn/#/auth-renew")
                            2 -> testUrl("https://lpmpm-test01.dotfashion.cn/canteen-device#/canteen-device/login")
                            3 -> showCustomUrlDialog()
                        }
                    }
                    .setNegativeButton("取消", null)
                    .create()
                dialog.show()
            }
        }
        
        private fun testUrl(url: String) {
            activity.runOnUiThread {
                Toast.makeText(activity, "正在测试URL: $url", Toast.LENGTH_SHORT).show()
                
                // 显示进度对话框
                val progressDialog = android.app.ProgressDialog(activity).apply {
                    setTitle("正在测试")
                    setMessage("正在测试URL连接: $url")
                    setCancelable(false)
                    show()
                }
                
                // 执行测试
                WebViewNetworkTester.testUrlConnection(url) { result ->
                    activity.runOnUiThread {
                        progressDialog.dismiss()
                        
                        // 显示测试结果
                        android.app.AlertDialog.Builder(activity)
                            .setTitle("测试结果: ${if (result.isSuccess) "成功" else "失败"}")
                            .setMessage(result.toString())
                            .setPositiveButton("确定", null)
                            .setNeutralButton("在WebView中测试") { _, _ ->
                                progressDialog.setMessage("在WebView中测试加载...")
                                progressDialog.show()
                                
                                WebViewNetworkTester.testWebViewLoading(webView, url) { webViewResult ->
                                    activity.runOnUiThread {
                                        progressDialog.dismiss()
                                        
                                        android.app.AlertDialog.Builder(activity)
                                            .setTitle("WebView加载测试")
                                            .setMessage(webViewResult.toString())
                                            .setPositiveButton("确定", null)
                                            .create()
                                            .show()
                                    }
                                }
                            }
                            .create()
                            .show()
                    }
                }
            }
        }
        
        private fun showCustomUrlDialog() {
            activity.runOnUiThread {
                val input = android.widget.EditText(activity).apply {
                    hint = "请输入URL (例如 https://example.com)"
                    setText("https://")
                }
                
                android.app.AlertDialog.Builder(activity)
                    .setTitle("输入URL")
                    .setView(input)
                    .setPositiveButton("测试") { _, _ ->
                        val url = input.text.toString().trim()
                        if (url.isNotEmpty() && android.webkit.URLUtil.isValidUrl(url)) {
                            testUrl(url)
                        } else {
                            Toast.makeText(activity, "请输入有效的URL", Toast.LENGTH_SHORT).show()
                        }
                    }
                    .setNegativeButton("取消", null)
                    .create()
                    .show()
            }
        }
    }
}

@Composable
fun Greeting(name: String, modifier: Modifier = Modifier) {
    Text(
        text = "1661Hello23 $name!",
        modifier = modifier
    )
}

@Preview(showBackground = true)
@Composable
fun GreetingPreview() {
    LpmpTestTheme {
        Greeting("Android")
    }
}