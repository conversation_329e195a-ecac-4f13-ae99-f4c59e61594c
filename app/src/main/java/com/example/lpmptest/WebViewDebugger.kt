package com.example.lpmptest

import android.util.Log
import android.webkit.WebView

/**
 * WebView调试工具类，用于增强WebView的调试能力
 */
class WebViewDebugger(private val webView: WebView) {

    companion object {
        private const val TAG = "WebViewDebugger"
    }

    /**
     * 注入调试JavaScript到网页中
     */
    fun injectDebugScript() {
        val debugScript = """
            (function() {
                // 捕获控制台日志
                var originalConsole = window.console;
                window.console = {
                    log: function() {
                        var args = Array.prototype.slice.call(arguments);
                        originalConsole.log.apply(originalConsole, args);
                        window.Android.logConsole('LOG', JSON.stringify(args));
                    },
                    error: function() {
                        var args = Array.prototype.slice.call(arguments);
                        originalConsole.error.apply(originalConsole, args);
                        window.Android.logConsole('ERROR', JSON.stringify(args));
                    },
                    warn: function() {
                        var args = Array.prototype.slice.call(arguments);
                        originalConsole.warn.apply(originalConsole, args);
                        window.Android.logConsole('WARN', JSON.stringify(args));
                    },
                    info: function() {
                        var args = Array.prototype.slice.call(arguments);
                        originalConsole.info.apply(originalConsole, args);
                        window.Android.logConsole('INFO', JSON.stringify(args));
                    }
                };
                
                // 捕获网络请求
                var originalFetch = window.fetch;
                if (originalFetch) {
                    window.fetch = function() {
                        var args = arguments;
                        window.Android.logNetwork('FETCH-REQUEST', JSON.stringify({
                            url: typeof args[0] === 'string' ? args[0] : args[0].url,
                            method: args[1] && args[1].method ? args[1].method : 'GET'
                        }));
                        return originalFetch.apply(this, args).then(function(response) {
                            window.Android.logNetwork('FETCH-RESPONSE', JSON.stringify({
                                url: response.url,
                                status: response.status,
                                statusText: response.statusText
                            }));
                            return response;
                        }).catch(function(error) {
                            window.Android.logNetwork('FETCH-ERROR', JSON.stringify({
                                message: error.message
                            }));
                            throw error;
                        });
                    };
                }
                
                // 捕获XMLHttpRequest
                var originalXHROpen = XMLHttpRequest.prototype.open;
                XMLHttpRequest.prototype.open = function() {
                    var args = arguments;
                    var method = args[0];
                    var url = args[1];
                    this._debugInfo = { method: method, url: url };
                    window.Android.logNetwork('XHR-OPEN', JSON.stringify({
                        method: method,
                        url: url
                    }));
                    return originalXHROpen.apply(this, args);
                };
                
                var originalXHRSend = XMLHttpRequest.prototype.send;
                XMLHttpRequest.prototype.send = function() {
                    var xhr = this;
                    var args = arguments;
                    
                    xhr.addEventListener('load', function() {
                        window.Android.logNetwork('XHR-LOAD', JSON.stringify({
                            url: xhr._debugInfo.url,
                            status: xhr.status,
                            statusText: xhr.statusText
                        }));
                    });
                    
                    xhr.addEventListener('error', function() {
                        window.Android.logNetwork('XHR-ERROR', JSON.stringify({
                            url: xhr._debugInfo.url,
                            status: xhr.status,
                            statusText: xhr.statusText || 'Unknown Error'
                        }));
                    });
                    
                    return originalXHRSend.apply(xhr, args);
                };
                
                // 捕获页面加载完成事件
                window.addEventListener('load', function() {
                    window.Android.logPageEvent('LOADED', window.location.href);
                });
                
                // 捕获页面错误
                window.addEventListener('error', function(event) {
                    window.Android.logPageEvent('ERROR', JSON.stringify({
                        message: event.message,
                        filename: event.filename,
                        lineno: event.lineno,
                        colno: event.colno
                    }));
                });
                
                // 捕获未处理的Promise拒绝
                window.addEventListener('unhandledrejection', function(event) {
                    window.Android.logPageEvent('UNHANDLED_REJECTION', 
                        event.reason ? event.reason.toString() : 'Unknown Rejection');
                });
                
                // 注入调试工具
                window.WebViewDebug = {
                    logElement: function(selector) {
                        var element = document.querySelector(selector);
                        if (element) {
                            window.Android.logConsole('ELEMENT', JSON.stringify({
                                selector: selector,
                                innerHTML: element.innerHTML.substring(0, 200) + '...',
                                outerHTML: element.outerHTML.substring(0, 200) + '...',
                                visible: element.offsetParent !== null
                            }));
                        } else {
                            window.Android.logConsole('ELEMENT', JSON.stringify({
                                selector: selector,
                                error: 'Element not found'
                            }));
                        }
                    },
                    
                    reportPageMetrics: function() {
                        window.Android.logConsole('PAGE_METRICS', JSON.stringify({
                            url: window.location.href,
                            title: document.title,
                            bodyElements: document.body ? document.body.childElementCount : 0,
                            scripts: document.scripts.length,
                            links: document.links.length,
                            images: document.images.length
                        }));
                    }
                };
                
                console.log('WebView调试工具已注入');
                setTimeout(function() {
                    window.WebViewDebug.reportPageMetrics();
                }, 1000);
            })();
        """.trimIndent()
        
        webView.evaluateJavascript(debugScript, null)
        Log.d(TAG, "已注入调试脚本")
    }
    
    /**
     * 获取当前网页的DOM结构
     */
    fun getDOMStructure() {
        val script = """
            (function() {
                function simplifyDOM(node, maxDepth, depth) {
                    if (!node || depth > maxDepth) return null;
                    
                    var result = {
                        tagName: node.tagName || 'TEXT',
                        id: node.id || '',
                        className: node.className || '',
                        text: node.textContent ? (node.textContent.trim().substring(0, 50)) : ''
                    };
                    
                    if (node.childNodes && node.childNodes.length > 0) {
                        result.children = [];
                        for (var i = 0; i < Math.min(node.childNodes.length, 10); i++) {
                            var childResult = simplifyDOM(node.childNodes[i], maxDepth, depth + 1);
                            if (childResult) {
                                result.children.push(childResult);
                            }
                        }
                    }
                    
                    return result;
                }
                
                return JSON.stringify(simplifyDOM(document.documentElement, 3, 0));
            })();
        """.trimIndent()
        
        webView.evaluateJavascript(script) { result ->
            Log.d(TAG, "DOM结构: $result")
        }
    }
    
    /**
     * 检测网页加载错误
     */
    fun checkLoadingErrors() {
        val script = """
            (function() {
                var errors = [];
                var missingResources = [];
                
                // 检查CSS资源
                var links = document.querySelectorAll('link[rel="stylesheet"]');
                for (var i = 0; i < links.length; i++) {
                    var link = links[i];
                    if (!link.sheet) {
                        missingResources.push({
                            type: 'CSS',
                            href: link.href
                        });
                    }
                }
                
                // 检查图片
                var images = document.querySelectorAll('img');
                for (var i = 0; i < images.length; i++) {
                    var img = images[i];
                    if (!img.complete || img.naturalHeight === 0) {
                        missingResources.push({
                            type: 'IMAGE',
                            src: img.src
                        });
                    }
                }
                
                // 返回收集的错误
                return JSON.stringify({
                    missingResources: missingResources,
                    url: window.location.href,
                    readyState: document.readyState
                });
            })();
        """.trimIndent()
        
        webView.evaluateJavascript(script) { result ->
            Log.d(TAG, "加载错误检测: $result")
        }
    }
    
    /**
     * 检查网络请求状态
     */
    fun checkNetworkStatus() {
        val script = """
            (function() {
                var networkStatus = {
                    online: navigator.onLine,
                    userAgent: navigator.userAgent,
                    vendor: navigator.vendor,
                    platform: navigator.platform,
                    language: navigator.language
                };
                
                return JSON.stringify(networkStatus);
            })();
        """.trimIndent()
        
        webView.evaluateJavascript(script) { result ->
            Log.d(TAG, "网络状态: $result")
        }
    }
}
